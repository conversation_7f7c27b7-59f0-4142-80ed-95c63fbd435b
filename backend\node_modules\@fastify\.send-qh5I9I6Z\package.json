{"name": "@fastify/send", "description": "Better streaming static file server with Range and conditional-GET support", "version": "2.1.0", "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>"], "main": "index.js", "types": "types/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/fastify/send.git"}, "keywords": ["static", "file", "server"], "dependencies": {"escape-html": "~1.0.3", "fast-decode-uri-component": "^1.0.1", "http-errors": "2.0.0", "mime": "^3.0.0", "@lukeed/ms": "^2.0.1"}, "devDependencies": {"@fastify/pre-commit": "^2.0.2", "@types/node": "^18.11.18", "after": "0.8.2", "benchmark": "^2.1.4", "snazzy": "^9.0.0", "standard": "^17.0.0", "supertest": "6.3.3", "tap": "^16.3.3", "tsd": "^0.28.0"}, "scripts": {"lint": "standard | snazzy", "lint:fix": "standard --fix | snazzy", "test": "npm run test:unit && npm run test:typescript", "test:coverage": "tap --coverage-report=html", "test:typescript": "tsd", "test:unit": "tap"}, "pre-commit": ["lint", "test"]}