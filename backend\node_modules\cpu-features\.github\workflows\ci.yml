name: CI

on:
  pull_request:
  push:
    branches: [ master ]

jobs:
  tests-linux:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        node-version: [10.x, 12.x, 14.x, 16.x, 18.x, 20.x, 22.x]
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
      - name: Check Node.js version
        run: node -pe process.versions
      - name: Install module
        run: npm install
      - name: Run tests
        run: npm test
  tests-macos:
    runs-on: macos-latest
    strategy:
      fail-fast: false
      matrix:
        node-version: [16.x, 18.x, 20.x, 22.x]
    env:
      PIP_DISABLE_PIP_VERSION_CHECK: 1
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
      - name: Install Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'
      - name: Check Node.js version
        run: node -pe process.versions
      - name: Install module
        run: npm install
      - name: Run tests
        run: npm test
  tests-windows:
    runs-on: windows-2019
    strategy:
      fail-fast: false
      matrix:
        node-version: [10.x, 12.x, 14.x, 16.x, 18.x, 20.x, 22.x]
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
      - name: Check Node.js version
        run: node -pe process.versions
      - name: Install module
        run: npm install
      - name: Run tests
        run: npm test
