name: amd64 Linux CMake

on:
  push:
  pull_request:
  schedule:
    # min hours day(month) month day(week)
    - cron: '0 0 7,22 * *'

jobs:
  # Building using the github runner environement directly.
  make:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Env
      run: make --directory=cmake/ci amd64_env
    - name: Devel
      run: make --directory=cmake/ci amd64_devel
    - name: Build
      run: make --directory=cmake/ci amd64_build
    - name: Test
      run: make --directory=cmake/ci amd64_test
    - name: Install Env
      run: make --directory=cmake/ci amd64_install_env
    - name: Install Devel
      run: make --directory=cmake/ci amd64_install_devel
    - name: Install Build
      run: make --directory=cmake/ci amd64_install_build
    - name: Install Test
      run: make --directory=cmake/ci amd64_install_test
