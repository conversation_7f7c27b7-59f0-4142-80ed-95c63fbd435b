{"version": 3, "file": "addon-search.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAqB,YAAID,IAEzBD,EAAkB,YAAIC,GACvB,CATD,CASGK,MAAM,I,8ICYT,mCACU,KAAAC,WAAgC,GAEhC,KAAAC,WAAqB,CA4C/B,CA1CE,SAAWC,GAmBT,OAlBKC,KAAKC,SACRD,KAAKC,OAAUC,IACbF,KAAKH,WAAWM,KAAKD,GACF,CACjBE,QAAS,KACP,IAAKJ,KAAKF,UACR,IAAK,IAAIO,EAAI,EAAGA,EAAIL,KAAKH,WAAWS,OAAQD,IAC1C,GAAIL,KAAKH,WAAWQ,KAAOH,EAEzB,YADAF,KAAKH,WAAWU,OAAOF,EAAG,E,KAUjCL,KAAKC,MACd,CAEO,IAAAO,CAAKC,EAASC,GACnB,MAAMC,EAA2B,GACjC,IAAK,IAAIN,EAAI,EAAGA,EAAIL,KAAKH,WAAWS,OAAQD,IAC1CM,EAAMR,KAAKH,KAAKH,WAAWQ,IAE7B,IAAK,IAAIA,EAAI,EAAGA,EAAIM,EAAML,OAAQD,IAChCM,EAAMN,GAAGO,UAAKC,EAAWJ,EAAMC,EAEnC,CAEO,OAAAN,GACLJ,KAAKc,iBACLd,KAAKF,WAAY,CACnB,CAEO,cAAAgB,GACDd,KAAKH,aACPG,KAAKH,WAAWS,OAAS,EAE7B,GAGF,wBAAgCS,EAAiBC,GAC/C,OAAOD,GAAKE,GAAKD,EAAGR,KAAKS,IAC3B,EAEA,2BAAmClB,EAAkBmB,GAEnD,OADAA,OAAQL,GACDd,GAAMkB,GAAKC,EAAQD,IAC5B,C,cCkBA,SAAgBE,EAAaC,GAC3B,IAAK,MAAMC,KAAKD,EACdC,EAAEjB,UAEJgB,EAAYd,OAAS,CACvB,C,mJAzFA,iCACY,KAAAgB,aAA8B,GAC9B,KAAAC,aAAuB,CAkCnC,CA7BS,OAAAnB,GACLJ,KAAKuB,aAAc,EACnB,IAAK,MAAMF,KAAKrB,KAAKsB,aACnBD,EAAEjB,UAEJJ,KAAKsB,aAAahB,OAAS,CAC7B,CAOO,QAAAkB,CAAgCH,GAErC,OADArB,KAAKsB,aAAanB,KAAKkB,GAChBA,CACT,CAOO,UAAAI,CAAkCJ,GACvC,MAAMK,EAAQ1B,KAAKsB,aAAaK,QAAQN,IACzB,IAAXK,GACF1B,KAAKsB,aAAaf,OAAOmB,EAAO,EAEpC,GAGF,wCAEU,KAAAH,aAAc,CAgCxB,CA3BE,SAAWK,GACT,OAAO5B,KAAKuB,iBAAcV,EAAYb,KAAK6B,MAC7C,CAKA,SAAWD,CAAMA,GACX5B,KAAKuB,aAAeK,IAAU5B,KAAK6B,SAGvC7B,KAAK6B,QAAQzB,UACbJ,KAAK6B,OAASD,EAChB,CAKO,KAAAE,GACL9B,KAAK4B,WAAQf,CACf,CAEO,OAAAT,GACLJ,KAAKuB,aAAc,EACnBvB,KAAK6B,QAAQzB,UACbJ,KAAK6B,YAAShB,CAChB,GAMF,wBAA6BkB,GAC3B,MAAO,CAAE3B,QAAS2B,EACpB,EAKA,iBAUA,qCAA0CC,GACxC,MAAO,CAAE5B,QAAS,IAAMe,EAAaa,GACvC,C,GC1GIC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBtB,IAAjBuB,EACH,OAAOA,EAAa5C,QAGrB,IAAIC,EAASwC,EAAyBE,GAAY,CAGjD3C,QAAS,CAAC,GAOX,OAHA6C,EAAoBF,GAAU1C,EAAQA,EAAOD,QAAS0C,GAG/CzC,EAAOD,OACf,C,mGCfA,eACA,SAoDM8C,EAAsB,qCAI5B,MAAaC,UAAoB,EAAAC,WAqB/B,WAAAC,CAAYC,GACVC,QAnBM,KAAAC,kBAAiC,IAAIC,IACrC,KAAAC,sBAAsC,GACtC,KAAAC,oBAAqD/C,KAAKwB,SAAS,IAAI,EAAAwB,mBAUvE,KAAAC,qBAAuB,EACvB,KAAAC,uBAAyB,IAAI,EAAAF,kBAEpB,KAAAG,oBAAsBnD,KAAKwB,SAAS,IAAI,EAAA4B,cACzC,KAAAC,mBAAqBrD,KAAKmD,oBAAoBpD,MAK5DC,KAAKsD,gBAAkBZ,GAASa,gBA1BJ,GA2B9B,CAEO,QAAAC,CAASC,GACdzD,KAAK0D,UAAYD,EACjBzD,KAAKwB,SAASxB,KAAK0D,UAAUC,eAAc,IAAM3D,KAAK4D,oBACtD5D,KAAKwB,SAASxB,KAAK0D,UAAUG,UAAS,IAAM7D,KAAK4D,oBACjD5D,KAAKwB,UAAS,IAAAsC,eAAa,IAAM9D,KAAK+D,qBACxC,CAEQ,cAAAH,GACF5D,KAAKgE,mBACPC,OAAOC,aAAalE,KAAKgE,mBAEvBhE,KAAKmE,mBAAqBnE,KAAKoE,oBAAoBC,cACrDrE,KAAKgE,kBAAoBM,YAAW,KAClC,MAAMC,EAAOvE,KAAKmE,kBAClBnE,KAAKmE,uBAAoBtD,EACzBb,KAAKwE,aAAaD,EAAO,IAAKvE,KAAKoE,mBAAoBK,aAAa,EAAMC,UAAU,GAAO,GAC1F,KAEP,CAEO,gBAAAX,CAAiBY,GACtB3E,KAAK+C,oBAAoBjB,SACzB,IAAAX,cAAanB,KAAK8C,uBAClB9C,KAAK8C,sBAAwB,GAC7B9C,KAAK4C,kBAAkBd,QAClB6C,IACH3E,KAAKmE,uBAAoBtD,EAE7B,CAEO,qBAAA+D,GACL5E,KAAK+C,oBAAoBjB,OAC3B,CASO,QAAA+C,CAASN,EAAcO,GAC5B,IAAK9E,KAAK0D,UACR,MAAM,IAAIqB,MAAM,6CAElB,MAAMC,GAAoBhF,KAAKoE,oBAAqBpE,KAAKiF,kBAAkBjF,KAAKoE,mBAAoBU,GACpG9E,KAAKoE,mBAAqBU,EACtBA,GAAeT,mBACcxD,IAA3Bb,KAAKmE,mBAAmCI,IAASvE,KAAKmE,mBAAqBa,IAC7EhF,KAAKkF,qBAAqBX,EAAMO,GAIpC,MAAMK,EAAQnF,KAAKoF,mBAAmBb,EAAMO,GAI5C,OAHA9E,KAAKqF,aAAaP,GAClB9E,KAAKmE,kBAAoBI,EAElBY,CACT,CAEQ,oBAAAD,CAAqBX,EAAcO,GACzC,IAAK9E,KAAK0D,UACR,MAAM,IAAIqB,MAAM,6CAElB,IAAKR,GAAwB,IAAhBA,EAAKjE,OAEhB,YADAN,KAAK+D,mBAGPe,EAAgBA,GAAiB,CAAC,EAGlC9E,KAAK+D,kBAAiB,GAEtB,MAAMuB,EAA8C,GACpD,IAAIC,EACAC,EAASxF,KAAKyF,MAAMlB,EAAM,EAAG,EAAGO,GACpC,KAAOU,IAAWD,GAAYG,MAAQF,EAAOE,KAAOH,GAAYI,MAAQH,EAAOG,QACzEL,EAA2BhF,QAAUN,KAAKsD,kBAG9CiC,EAAaC,EACbF,EAA2BnF,KAAKoF,GAChCC,EAASxF,KAAKyF,MACZlB,EACAgB,EAAWI,IAAMJ,EAAWhB,KAAKjE,QAAUN,KAAK0D,UAAUkC,KAAOL,EAAWG,IAAM,EAAIH,EAAWG,IACjGH,EAAWI,IAAMJ,EAAWhB,KAAKjE,QAAUN,KAAK0D,UAAUkC,KAAO,EAAIL,EAAWI,IAAM,EACtFb,GAGJ,IAAK,MAAMe,KAASP,EAA4B,CAC9C,MAAMQ,EAAa9F,KAAK+F,wBAAwBF,EAAOf,EAAcT,aACjEyB,IACF9F,KAAK4C,kBAAkBoD,IAAIF,EAAWG,OAAOC,MAC7ClG,KAAK8C,sBAAsB3C,KAAK,CAAE2F,aAAYD,QAAO,OAAAzF,GAAY0F,EAAW1F,SAAW,I,CAG7F,CAEQ,KAAAqF,CAAMlB,EAAc4B,EAAkBC,EAAkBtB,GAC9D,IAAK9E,KAAK0D,YAAca,GAAwB,IAAhBA,EAAKjE,OAGnC,OAFAN,KAAK0D,WAAW2C,sBAChBrG,KAAK+D,mBAGP,GAAIqC,EAAWpG,KAAK0D,UAAUkC,KAC5B,MAAM,IAAIb,MAAM,gBAAgBqB,8BAAqCpG,KAAK0D,UAAUkC,aAGtF,IAAIJ,EAEJxF,KAAKsG,kBAEL,MAAMC,EAAkC,CACtCJ,WACAC,YAMF,GAFAZ,EAASxF,KAAKwG,YAAYjC,EAAMgC,EAAgBzB,IAE3CU,EAEH,IAAK,IAAIiB,EAAIN,EAAW,EAAGM,EAAIzG,KAAK0D,UAAUgD,OAAOC,OAAOC,MAAQ5G,KAAK0D,UAAUmD,OACjFN,EAAeJ,SAAWM,EAC1BF,EAAeH,SAAW,EAG1BZ,EAASxF,KAAKwG,YAAYjC,EAAMgC,EAAgBzB,IAC5CU,GANmFiB,KAW3F,OAAOjB,CACT,CAEQ,kBAAAJ,CAAmBb,EAAcO,GACvC,IAAK9E,KAAK0D,YAAca,GAAwB,IAAhBA,EAAKjE,OAGnC,OAFAN,KAAK0D,WAAW2C,iBAChBrG,KAAK+D,oBACE,EAGT,MAAM+C,EAAkB9G,KAAK0D,UAAUqD,uBACvC/G,KAAK0D,UAAU2C,iBAEf,IAAID,EAAW,EACXD,EAAW,EACXW,IACE9G,KAAKmE,oBAAsBI,GAC7B6B,EAAWU,EAAgBE,IAAIC,EAC/Bd,EAAWW,EAAgBE,IAAIP,IAE/BL,EAAWU,EAAgBI,MAAMD,EACjCd,EAAWW,EAAgBI,MAAMT,IAIrCzG,KAAKsG,kBAEL,MAAMC,EAAkC,CACtCJ,WACAC,YAIF,IAAIZ,EAASxF,KAAKwG,YAAYjC,EAAMgC,EAAgBzB,GAEpD,IAAKU,EAEH,IAAK,IAAIiB,EAAIN,EAAW,EAAGM,EAAIzG,KAAK0D,UAAUgD,OAAOC,OAAOC,MAAQ5G,KAAK0D,UAAUmD,OACjFN,EAAeJ,SAAWM,EAC1BF,EAAeH,SAAW,EAG1BZ,EAASxF,KAAKwG,YAAYjC,EAAMgC,EAAgBzB,IAC5CU,GANmFiB,KAY3F,IAAKjB,GAAuB,IAAbW,EACb,IAAK,IAAIM,EAAI,EAAGA,EAAIN,IAClBI,EAAeJ,SAAWM,EAC1BF,EAAeH,SAAW,EAC1BZ,EAASxF,KAAKwG,YAAYjC,EAAMgC,EAAgBzB,IAC5CU,GAJwBiB,KAkBhC,OAPKjB,GAAUsB,IACbP,EAAeJ,SAAWW,EAAgBI,MAAMT,EAChDF,EAAeH,SAAW,EAC1BZ,EAASxF,KAAKwG,YAAYjC,EAAMgC,EAAgBzB,IAI3C9E,KAAKmH,cAAc3B,EAAQV,GAAeT,YAAaS,GAAeJ,SAC/E,CAQO,YAAAF,CAAaD,EAAcO,GAChC,IAAK9E,KAAK0D,UACR,MAAM,IAAIqB,MAAM,6CAElB,MAAMC,GAAoBhF,KAAKoE,oBAAqBpE,KAAKiF,kBAAkBjF,KAAKoE,mBAAoBU,GACpG9E,KAAKoE,mBAAqBU,EACtBA,GAAeT,mBACcxD,IAA3Bb,KAAKmE,mBAAmCI,IAASvE,KAAKmE,mBAAqBa,IAC7EhF,KAAKkF,qBAAqBX,EAAMO,GAIpC,MAAMK,EAAQnF,KAAKoH,uBAAuB7C,EAAMO,GAIhD,OAHA9E,KAAKqF,aAAaP,GAClB9E,KAAKmE,kBAAoBI,EAElBY,CACT,CAEQ,iBAAAF,CAAkBoC,EAAmCvC,GAC3D,QAAKA,IAGDuC,EAAkBC,gBAAkBxC,EAAcwC,eAGlDD,EAAkBE,QAAUzC,EAAcyC,OAG1CF,EAAkBG,YAAc1C,EAAc0C,UAIpD,CAEQ,YAAAnC,CAAaP,GACnB,GAAIA,GAAeT,YAAa,CAC9B,IAAIoD,GAAe,EACnB,GAAIzH,KAAK+C,oBAAoBnB,MAAO,CAClC,MAAM8F,EAAgB1H,KAAK+C,oBAAoBnB,MAAMiE,MACrD,IAAK,IAAIxF,EAAI,EAAGA,EAAIL,KAAK8C,sBAAsBxC,OAAQD,IAAK,CAC1D,MAAMwF,EAAQ7F,KAAK8C,sBAAsBzC,GAAGwF,MAC5C,GAAIA,EAAMH,MAAQgC,EAAchC,KAAOG,EAAMF,MAAQ+B,EAAc/B,KAAOE,EAAM8B,OAASD,EAAcC,KAAM,CAC3GF,EAAcpH,EACd,K,GAINL,KAAKmD,oBAAoB3C,KAAK,CAAEiH,cAAaG,YAAa5H,KAAK8C,sBAAsBxC,Q,CAEzF,CAEQ,sBAAA8G,CAAuB7C,EAAcO,GAC3C,IAAK9E,KAAK0D,UACR,MAAM,IAAIqB,MAAM,6CAElB,IAAK/E,KAAK0D,YAAca,GAAwB,IAAhBA,EAAKjE,OAGnC,OAFAN,KAAK0D,WAAW2C,iBAChBrG,KAAK+D,oBACE,EAGT,MAAM+C,EAAkB9G,KAAK0D,UAAUqD,uBACvC/G,KAAK0D,UAAU2C,iBAEf,IAAIF,EAAWnG,KAAK0D,UAAUgD,OAAOC,OAAOC,MAAQ5G,KAAK0D,UAAUmD,KAAO,EACtET,EAAWpG,KAAK0D,UAAUkC,KAC9B,MAAMiC,GAAkB,EAExB7H,KAAKsG,kBACL,MAAMC,EAAkC,CACtCJ,WACAC,YAGF,IAAIZ,EAoBJ,GAnBIsB,IACFP,EAAeJ,SAAWA,EAAWW,EAAgBI,MAAMT,EAC3DF,EAAeH,SAAWA,EAAWU,EAAgBI,MAAMD,EACvDjH,KAAKmE,oBAAsBI,IAE7BiB,EAASxF,KAAKwG,YAAYjC,EAAMgC,EAAgBzB,GAAe,GAC1DU,IAEHe,EAAeJ,SAAWA,EAAWW,EAAgBE,IAAIP,EACzDF,EAAeH,SAAWA,EAAWU,EAAgBE,IAAIC,KAK1DzB,IACHA,EAASxF,KAAKwG,YAAYjC,EAAMgC,EAAgBzB,EAAe+C,KAI5DrC,EAAQ,CACXe,EAAeH,SAAW0B,KAAKC,IAAIxB,EAAeH,SAAUpG,KAAK0D,UAAUkC,MAC3E,IAAK,IAAIa,EAAIN,EAAW,EAAGM,GAAK,IAC9BF,EAAeJ,SAAWM,EAC1BjB,EAASxF,KAAKwG,YAAYjC,EAAMgC,EAAgBzB,EAAe+C,IAC3DrC,GAH6BiB,K,CASrC,IAAKjB,GAAUW,IAAcnG,KAAK0D,UAAUgD,OAAOC,OAAOC,MAAQ5G,KAAK0D,UAAUmD,KAAO,EACtF,IAAK,IAAIJ,EAAKzG,KAAK0D,UAAUgD,OAAOC,OAAOC,MAAQ5G,KAAK0D,UAAUmD,KAAO,EAAIJ,GAAKN,IAChFI,EAAeJ,SAAWM,EAC1BjB,EAASxF,KAAKwG,YAAYjC,EAAMgC,EAAgBzB,EAAe+C,IAC3DrC,GAHsFiB,KAU9F,OAAOzG,KAAKmH,cAAc3B,EAAQV,GAAeT,YAAaS,GAAeJ,SAC/E,CAKQ,eAAA4B,GACN,MAAM7C,EAAWzD,KAAK0D,UACjB1D,KAAKgI,cACRhI,KAAKgI,YAAc,IAAIC,MAAMxE,EAASiD,OAAOC,OAAOrG,QACpDN,KAAKkD,uBAAuBtB,OAAQ,IAAAsG,2BAA0B,CAC5DzE,EAAS0E,YAAW,IAAMnI,KAAKoI,uBAC/B3E,EAAS4E,cAAa,IAAMrI,KAAKoI,uBACjC3E,EAASI,UAAS,IAAM7D,KAAKoI,0BAIjCnE,OAAOC,aAAalE,KAAKiD,sBACzBjD,KAAKiD,qBAAuBgB,OAAOK,YAAW,IAAMtE,KAAKoI,sBAvX5B,KAwX/B,CAEQ,kBAAAA,GACNpI,KAAKgI,iBAAcnH,EACnBb,KAAKkD,uBAAuBpB,QACxB9B,KAAKiD,uBACPgB,OAAOC,aAAalE,KAAKiD,sBACzBjD,KAAKiD,qBAAuB,EAEhC,CASQ,YAAAqF,CAAaC,EAAqBrC,EAAc3B,GACtD,OAAyB,IAAhBgE,GAAuBjG,EAAoBkG,SAAStC,EAAKqC,EAAc,OAC3EA,EAAchE,EAAKjE,SAAY4F,EAAK5F,QAAYgC,EAAoBkG,SAAStC,EAAKqC,EAAchE,EAAKjE,SAC5G,CAcU,WAAAkG,CAAYjC,EAAcgC,EAAiCzB,EAAgC,CAAC,EAAG+C,GAA2B,GAClI,MAAMpE,EAAWzD,KAAK0D,UAChBgC,EAAMa,EAAeJ,SACrBR,EAAMY,EAAeH,SAGrBqC,EAAYhF,EAASiD,OAAOC,OAAO+B,QAAQhD,GACjD,GAAI+C,GAAWE,UACb,OAAId,OACFtB,EAAeH,UAAY3C,EAASmC,OAMtCW,EAAeJ,WACfI,EAAeH,UAAY3C,EAASmC,KAC7B5F,KAAKwG,YAAYjC,EAAMgC,EAAgBzB,IAEhD,IAAI8D,EAAQ5I,KAAKgI,cAActC,GAC1BkD,IACHA,EAAQ5I,KAAK6I,qCAAqCnD,GAAK,GACnD1F,KAAKgI,cACPhI,KAAKgI,YAAYtC,GAAOkD,IAG5B,MAAOE,EAAYC,GAAWH,EAExBI,EAAShJ,KAAKiJ,0BAA0BvD,EAAKC,GAC7CuD,EAAapE,EAAcwC,cAAgB/C,EAAOA,EAAK4E,cACvDC,EAAmBtE,EAAcwC,cAAgBwB,EAAaA,EAAWK,cAE/E,IAAI1B,GAAe,EACnB,GAAI3C,EAAcyC,MAAO,CACvB,MAAM8B,EAAcC,OAAOJ,EAAY,KACvC,IAAIK,EACJ,GAAI1B,EAEF,KAAO0B,EAAYF,EAAYG,KAAKJ,EAAiBK,MAAM,EAAGT,KAC5DvB,EAAc4B,EAAYK,UAAYH,EAAU,GAAGjJ,OACnDiE,EAAOgF,EAAU,GACjBF,EAAYK,WAAcnF,EAAKjE,OAAS,OAG1CiJ,EAAYF,EAAYG,KAAKJ,EAAiBK,MAAMT,IAChDO,GAAaA,EAAU,GAAGjJ,OAAS,IACrCmH,EAAcuB,GAAUK,EAAYK,UAAYH,EAAU,GAAGjJ,QAC7DiE,EAAOgF,EAAU,G,MAIjB1B,EACEmB,EAASE,EAAW5I,QAAU,IAChCmH,EAAc2B,EAAiBO,YAAYT,EAAYF,EAASE,EAAW5I,SAG7EmH,EAAc2B,EAAiBzH,QAAQuH,EAAYF,GAIvD,GAAIvB,GAAe,EAAG,CACpB,GAAI3C,EAAc0C,YAAcxH,KAAKsI,aAAab,EAAa2B,EAAkB7E,GAC/E,OAKF,IAAIqF,EAAiB,EACrB,KAAOA,EAAiBb,EAAQzI,OAAS,GAAKmH,GAAesB,EAAQa,EAAiB,IACpFA,IAEF,IAAIC,EAAeD,EACnB,KAAOC,EAAed,EAAQzI,OAAS,GAAKmH,EAAclD,EAAKjE,QAAUyI,EAAQc,EAAe,IAC9FA,IAEF,MAAMC,EAAiBrC,EAAcsB,EAAQa,GACvCG,EAAetC,EAAclD,EAAKjE,OAASyI,EAAQc,GACnDG,EAAgBhK,KAAKiK,0BAA0BvE,EAAMkE,EAAgBE,GAI3E,MAAO,CACLvF,OACAoB,IAAKqE,EACLtE,IAAKA,EAAMkE,EACXjC,KAPkB3H,KAAKiK,0BAA0BvE,EAAMmE,EAAcE,GAC5CC,EAAgBvG,EAASmC,MAAQiE,EAAeD,G,CAS/E,CAEQ,yBAAAK,CAA0BvE,EAAasD,GAC7C,MAAM9C,EAAOlG,KAAK0D,UAAWgD,OAAOC,OAAO+B,QAAQhD,GACnD,IAAKQ,EACH,OAAO,EAET,IAAK,IAAI7F,EAAI,EAAGA,EAAI2I,EAAQ3I,IAAK,CAC/B,MAAM6J,EAAOhE,EAAKiE,QAAQ9J,GAC1B,IAAK6J,EACH,MAGF,MAAME,EAAOF,EAAKG,WACdD,EAAK9J,OAAS,IAChB0I,GAAUoB,EAAK9J,OAAS,GAI1B,MAAMgK,EAAWpE,EAAKiE,QAAQ9J,EAAI,GAC9BiK,GAAoC,IAAxBA,EAASC,YACvBvB,G,CAGJ,OAAOA,CACT,CAEQ,yBAAAC,CAA0B9C,EAAkBP,GAClD,MAAMnC,EAAWzD,KAAK0D,UACtB,IAAI8G,EAAYrE,EACZ6C,EAAS,EACT9C,EAAOzC,EAASiD,OAAOC,OAAO+B,QAAQ8B,GAC1C,KAAO5E,EAAO,GAAKM,GAAM,CACvB,IAAK,IAAI7F,EAAI,EAAGA,EAAIuF,GAAQvF,EAAIoD,EAASmC,KAAMvF,IAAK,CAClD,MAAM6J,EAAOhE,EAAKiE,QAAQ9J,GAC1B,IAAK6J,EACH,MAEEA,EAAKK,aAEPvB,GAA6B,IAAnBkB,EAAKO,UAAkB,EAAIP,EAAKG,WAAW/J,O,CAKzD,GAFAkK,IACAtE,EAAOzC,EAASiD,OAAOC,OAAO+B,QAAQ8B,GAClCtE,IAASA,EAAKyC,UAChB,MAEF/C,GAAQnC,EAASmC,I,CAEnB,OAAOoD,CACT,CAUQ,oCAAAH,CAAqC2B,EAAmBE,GAC9D,MAAMjH,EAAWzD,KAAK0D,UAChBiH,EAAU,GACVC,EAAc,CAAC,GACrB,IAAI1E,EAAOzC,EAASiD,OAAOC,OAAO+B,QAAQ8B,GAC1C,KAAOtE,GAAM,CACX,MAAM2E,EAAWpH,EAASiD,OAAOC,OAAO+B,QAAQ8B,EAAY,GACtDM,IAAkBD,GAAWA,EAASlC,UAC5C,IAAIoC,EAAS7E,EAAK8E,mBAAmBF,GAAmBJ,GACxD,GAAII,GAAmBD,EAAU,CAC/B,MAAMI,EAAW/E,EAAKiE,QAAQjE,EAAK5F,OAAS,GACrB2K,GAAmC,IAAvBA,EAASR,WAA2C,IAAxBQ,EAASV,YAEd,IAApCM,EAASV,QAAQ,IAAII,aACzCQ,EAASA,EAAOtB,MAAM,GAAI,G,CAI9B,GADAkB,EAAQxK,KAAK4K,IACTD,EAGF,MAFAF,EAAYzK,KAAKyK,EAAYA,EAAYtK,OAAS,GAAKyK,EAAOzK,QAIhEkK,IACAtE,EAAO2E,C,CAET,MAAO,CAACF,EAAQO,KAAK,IAAKN,EAC5B,CAOQ,aAAAzD,CAAc3B,EAAmC9C,EAAoCgC,GAC3F,MAAMjB,EAAWzD,KAAK0D,UAEtB,GADA1D,KAAK+C,oBAAoBjB,SACpB0D,EAEH,OADA/B,EAAS4C,kBACF,EAGT,GADA5C,EAAS0H,OAAO3F,EAAOG,IAAKH,EAAOE,IAAKF,EAAOmC,MAC3CjF,EAAS,CACX,MAAMuD,EAASxC,EAAS2H,gBAAgB3H,EAASiD,OAAOC,OAAOC,MAAQnD,EAASiD,OAAOC,OAAO0E,QAAU7F,EAAOE,KAC/G,GAAIO,EAAQ,CACV,MAAMH,EAAarC,EAAS6H,mBAAmB,CAC7CrF,SACAgB,EAAGzB,EAAOG,IACV4F,MAAO/F,EAAOmC,KACd6D,gBAAiB9I,EAAQ+I,sBACzBC,MAAO,MACPC,qBAAsB,CACpBC,MAAOlJ,EAAQmJ,iCAGnB,GAAI/F,EAAY,CACd,MAAM1E,EAA6B,GACnCA,EAAYjB,KAAK8F,GACjB7E,EAAYjB,KAAK2F,EAAWgG,UAAU7K,GAAMjB,KAAK+L,aAAa9K,EAAGyB,EAAQsJ,mBAAmB,MAC5F5K,EAAYjB,KAAK2F,EAAWmG,WAAU,KAAM,IAAA9K,cAAaC,MACzDpB,KAAK+C,oBAAoBnB,MAAQ,CAAEkE,aAAYD,MAAOL,EAAQ,OAAApF,GAAY0F,EAAW1F,SAAW,E,GAKtG,IAAKsE,IAECc,EAAOE,KAAQjC,EAASiD,OAAOC,OAAOuF,UAAYzI,EAASoD,MAASrB,EAAOE,IAAMjC,EAASiD,OAAOC,OAAOuF,WAAW,CACrH,IAAIC,EAAS3G,EAAOE,IAAMjC,EAASiD,OAAOC,OAAOuF,UACjDC,GAAUrE,KAAKsE,MAAM3I,EAASoD,KAAO,GACrCpD,EAAS4I,YAAYF,E,CAGzB,OAAO,CACT,CASQ,YAAAJ,CAAaO,EAAsBC,EAAiCC,GACrEF,EAAQG,UAAUC,SAAS,kCAC9BJ,EAAQG,UAAUzG,IAAI,gCAClBuG,IACFD,EAAQK,MAAMC,QAAU,aAAaL,MAGrCC,GACFF,EAAQG,UAAUzG,IAAI,sCAE1B,CAQQ,uBAAAD,CAAwBP,EAAuB9C,GACrD,MAAMe,EAAWzD,KAAK0D,UAChBuC,EAASxC,EAAS2H,gBAAgB3H,EAASiD,OAAOC,OAAOC,MAAQnD,EAASiD,OAAOC,OAAO0E,QAAU7F,EAAOE,KAC/G,IAAKO,EACH,OAEF,MAAM4G,EAAuBpJ,EAAS6H,mBAAmB,CACvDrF,SACAgB,EAAGzB,EAAOG,IACV4F,MAAO/F,EAAOmC,KACd6D,gBAAiB9I,EAAQoK,gBACzBnB,qBAAsB3L,KAAK4C,kBAAkBmK,IAAI9G,EAAOC,WAAQrF,EAAY,CAC1E+K,MAAOlJ,EAAQsK,mBACfC,SAAU,YAGd,GAAIJ,EAAsB,CACxB,MAAMzL,EAA6B,GACnCA,EAAYjB,KAAK8F,GACjB7E,EAAYjB,KAAK0M,EAAqBf,UAAU7K,GAAMjB,KAAK+L,aAAa9K,EAAGyB,EAAQwK,aAAa,MAChG9L,EAAYjB,KAAK0M,EAAqBZ,WAAU,KAAM,IAAA9K,cAAaC,K,CAErE,OAAOyL,CACT,EAzqBF,e", "sources": ["webpack://SearchAddon/webpack/universalModuleDefinition", "webpack://SearchAddon/../../src/common/EventEmitter.ts", "webpack://SearchAddon/../../src/common/Lifecycle.ts", "webpack://SearchAddon/webpack/bootstrap", "webpack://SearchAddon/./src/SearchAddon.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"SearchAddon\"] = factory();\n\telse\n\t\troot[\"SearchAddon\"] = factory();\n})(self, () => {\nreturn ", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IDisposable } from 'common/Types';\n\ninterface IListener<T, U = void> {\n  (arg1: T, arg2: U): void;\n}\n\nexport interface IEvent<T, U = void> {\n  (listener: (arg1: T, arg2: U) => any): IDisposable;\n}\n\nexport interface IEventEmitter<T, U = void> {\n  event: IEvent<T, U>;\n  fire(arg1: T, arg2: U): void;\n  dispose(): void;\n}\n\nexport class EventEmitter<T, U = void> implements IEventEmitter<T, U> {\n  private _listeners: IListener<T, U>[] = [];\n  private _event?: IEvent<T, U>;\n  private _disposed: boolean = false;\n\n  public get event(): IEvent<T, U> {\n    if (!this._event) {\n      this._event = (listener: (arg1: T, arg2: U) => any) => {\n        this._listeners.push(listener);\n        const disposable = {\n          dispose: () => {\n            if (!this._disposed) {\n              for (let i = 0; i < this._listeners.length; i++) {\n                if (this._listeners[i] === listener) {\n                  this._listeners.splice(i, 1);\n                  return;\n                }\n              }\n            }\n          }\n        };\n        return disposable;\n      };\n    }\n    return this._event;\n  }\n\n  public fire(arg1: T, arg2: U): void {\n    const queue: IListener<T, U>[] = [];\n    for (let i = 0; i < this._listeners.length; i++) {\n      queue.push(this._listeners[i]);\n    }\n    for (let i = 0; i < queue.length; i++) {\n      queue[i].call(undefined, arg1, arg2);\n    }\n  }\n\n  public dispose(): void {\n    this.clearListeners();\n    this._disposed = true;\n  }\n\n  public clearListeners(): void {\n    if (this._listeners) {\n      this._listeners.length = 0;\n    }\n  }\n}\n\nexport function forwardEvent<T>(from: IEvent<T>, to: IEventEmitter<T>): IDisposable {\n  return from(e => to.fire(e));\n}\n\nexport function runAndSubscribe<T>(event: IEvent<T>, handler: (e: T | undefined) => any): IDisposable {\n  handler(undefined);\n  return event(e => handler(e));\n}\n", "/**\n * Copyright (c) 2018 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IDisposable } from 'common/Types';\n\n/**\n * A base class that can be extended to provide convenience methods for managing the lifecycle of an\n * object and its components.\n */\nexport abstract class Disposable implements IDisposable {\n  protected _disposables: IDisposable[] = [];\n  protected _isDisposed: boolean = false;\n\n  /**\n   * Disposes the object, triggering the `dispose` method on all registered IDisposables.\n   */\n  public dispose(): void {\n    this._isDisposed = true;\n    for (const d of this._disposables) {\n      d.dispose();\n    }\n    this._disposables.length = 0;\n  }\n\n  /**\n   * Registers a disposable object.\n   * @param d The disposable to register.\n   * @returns The disposable.\n   */\n  public register<T extends IDisposable>(d: T): T {\n    this._disposables.push(d);\n    return d;\n  }\n\n  /**\n   * Unregisters a disposable object if it has been registered, if not do\n   * nothing.\n   * @param d The disposable to unregister.\n   */\n  public unregister<T extends IDisposable>(d: T): void {\n    const index = this._disposables.indexOf(d);\n    if (index !== -1) {\n      this._disposables.splice(index, 1);\n    }\n  }\n}\n\nexport class MutableDisposable<T extends IDisposable> implements IDisposable {\n  private _value?: T;\n  private _isDisposed = false;\n\n  /**\n   * Gets the value if it exists.\n   */\n  public get value(): T | undefined {\n    return this._isDisposed ? undefined : this._value;\n  }\n\n  /**\n   * Sets the value, disposing of the old value if it exists.\n   */\n  public set value(value: T | undefined) {\n    if (this._isDisposed || value === this._value) {\n      return;\n    }\n    this._value?.dispose();\n    this._value = value;\n  }\n\n  /**\n   * Resets the stored value and disposes of the previously stored value.\n   */\n  public clear(): void {\n    this.value = undefined;\n  }\n\n  public dispose(): void {\n    this._isDisposed = true;\n    this._value?.dispose();\n    this._value = undefined;\n  }\n}\n\n/**\n * Wrap a function in a disposable.\n */\nexport function toDisposable(f: () => void): IDisposable {\n  return { dispose: f };\n}\n\n/**\n * Dispose of all disposables in an array and set its length to 0.\n */\nexport function disposeArray(disposables: IDisposable[]): void {\n  for (const d of disposables) {\n    d.dispose();\n  }\n  disposables.length = 0;\n}\n\n/**\n * Creates a disposable that will dispose of an array of disposables when disposed.\n */\nexport function getDisposeArrayDisposable(array: IDisposable[]): IDisposable {\n  return { dispose: () => disposeArray(array) };\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport type { Terminal, IDisposable, ITerminalAddon, IDecoration } from '@xterm/xterm';\nimport type { SearchAddon as ISearchApi } from '@xterm/addon-search';\nimport { EventEmitter } from 'common/EventEmitter';\nimport { Disposable, toDisposable, disposeArray, MutableDisposable, getDisposeArrayDisposable } from 'common/Lifecycle';\n\nexport interface ISearchOptions {\n  regex?: boolean;\n  wholeWord?: boolean;\n  caseSensitive?: boolean;\n  incremental?: boolean;\n  decorations?: ISearchDecorationOptions;\n  noScroll?: boolean;\n}\n\ninterface ISearchDecorationOptions {\n  matchBackground?: string;\n  matchBorder?: string;\n  matchOverviewRuler: string;\n  activeMatchBackground?: string;\n  activeMatchBorder?: string;\n  activeMatchColorOverviewRuler: string;\n}\n\nexport interface ISearchPosition {\n  startCol: number;\n  startRow: number;\n}\n\nexport interface ISearchAddonOptions {\n  highlightLimit: number;\n}\n\nexport interface ISearchResult {\n  term: string;\n  col: number;\n  row: number;\n  size: number;\n}\n\ntype LineCacheEntry = [\n  /**\n   * The string representation of a line (as opposed to the buffer cell representation).\n   */\n  lineAsString: string,\n  /**\n   * The offsets where each line starts when the entry describes a wrapped line.\n   */\n  lineOffsets: number[]\n];\n\ninterface IHighlight extends IDisposable {\n  decoration: IDecoration;\n  match: ISearchResult;\n}\n\nconst NON_WORD_CHARACTERS = ' ~!@#$%^&*()+`-=[]{}|\\\\;:\"\\',./<>?';\nconst LINES_CACHE_TIME_TO_LIVE = 15 * 1000; // 15 secs\nconst DEFAULT_HIGHLIGHT_LIMIT = 1000;\n\nexport class SearchAddon extends Disposable implements ITerminalAddon , ISearchApi {\n  private _terminal: Terminal | undefined;\n  private _cachedSearchTerm: string | undefined;\n  private _highlightedLines: Set<number> = new Set();\n  private _highlightDecorations: IHighlight[] = [];\n  private _selectedDecoration: MutableDisposable<IHighlight> = this.register(new MutableDisposable());\n  private _highlightLimit: number;\n  private _lastSearchOptions: ISearchOptions | undefined;\n  private _highlightTimeout: number | undefined;\n  /**\n   * translateBufferLineToStringWithWrap is a fairly expensive call.\n   * We memoize the calls into an array that has a time based ttl.\n   * _linesCache is also invalidated when the terminal cursor moves.\n   */\n  private _linesCache: LineCacheEntry[] | undefined;\n  private _linesCacheTimeoutId = 0;\n  private _linesCacheDisposables = new MutableDisposable();\n\n  private readonly _onDidChangeResults = this.register(new EventEmitter<{ resultIndex: number, resultCount: number }>());\n  public readonly onDidChangeResults = this._onDidChangeResults.event;\n\n  constructor(options?: Partial<ISearchAddonOptions>) {\n    super();\n\n    this._highlightLimit = options?.highlightLimit ?? DEFAULT_HIGHLIGHT_LIMIT;\n  }\n\n  public activate(terminal: Terminal): void {\n    this._terminal = terminal;\n    this.register(this._terminal.onWriteParsed(() => this._updateMatches()));\n    this.register(this._terminal.onResize(() => this._updateMatches()));\n    this.register(toDisposable(() => this.clearDecorations()));\n  }\n\n  private _updateMatches(): void {\n    if (this._highlightTimeout) {\n      window.clearTimeout(this._highlightTimeout);\n    }\n    if (this._cachedSearchTerm && this._lastSearchOptions?.decorations) {\n      this._highlightTimeout = setTimeout(() => {\n        const term = this._cachedSearchTerm;\n        this._cachedSearchTerm = undefined;\n        this.findPrevious(term!, { ...this._lastSearchOptions, incremental: true, noScroll: true });\n      }, 200);\n    }\n  }\n\n  public clearDecorations(retainCachedSearchTerm?: boolean): void {\n    this._selectedDecoration.clear();\n    disposeArray(this._highlightDecorations);\n    this._highlightDecorations = [];\n    this._highlightedLines.clear();\n    if (!retainCachedSearchTerm) {\n      this._cachedSearchTerm = undefined;\n    }\n  }\n\n  public clearActiveDecoration(): void {\n    this._selectedDecoration.clear();\n  }\n\n  /**\n   * Find the next instance of the term, then scroll to and select it. If it\n   * doesn't exist, do nothing.\n   * @param term The search term.\n   * @param searchOptions Search options.\n   * @returns Whether a result was found.\n   */\n  public findNext(term: string, searchOptions?: ISearchOptions): boolean {\n    if (!this._terminal) {\n      throw new Error('Cannot use addon until it has been loaded');\n    }\n    const didOptionsChanged = this._lastSearchOptions ? this._didOptionsChange(this._lastSearchOptions, searchOptions) : true;\n    this._lastSearchOptions = searchOptions;\n    if (searchOptions?.decorations) {\n      if (this._cachedSearchTerm === undefined || term !== this._cachedSearchTerm || didOptionsChanged) {\n        this._highlightAllMatches(term, searchOptions);\n      }\n    }\n\n    const found = this._findNextAndSelect(term, searchOptions);\n    this._fireResults(searchOptions);\n    this._cachedSearchTerm = term;\n\n    return found;\n  }\n\n  private _highlightAllMatches(term: string, searchOptions: ISearchOptions): void {\n    if (!this._terminal) {\n      throw new Error('Cannot use addon until it has been loaded');\n    }\n    if (!term || term.length === 0) {\n      this.clearDecorations();\n      return;\n    }\n    searchOptions = searchOptions || {};\n\n    // new search, clear out the old decorations\n    this.clearDecorations(true);\n\n    const searchResultsWithHighlight: ISearchResult[] = [];\n    let prevResult: ISearchResult | undefined = undefined;\n    let result = this._find(term, 0, 0, searchOptions);\n    while (result && (prevResult?.row !== result.row || prevResult?.col !== result.col)) {\n      if (searchResultsWithHighlight.length >= this._highlightLimit) {\n        break;\n      }\n      prevResult = result;\n      searchResultsWithHighlight.push(prevResult);\n      result = this._find(\n        term,\n        prevResult.col + prevResult.term.length >= this._terminal.cols ? prevResult.row + 1 : prevResult.row,\n        prevResult.col + prevResult.term.length >= this._terminal.cols ? 0 : prevResult.col + 1,\n        searchOptions\n      );\n    }\n    for (const match of searchResultsWithHighlight) {\n      const decoration = this._createResultDecoration(match, searchOptions.decorations!);\n      if (decoration) {\n        this._highlightedLines.add(decoration.marker.line);\n        this._highlightDecorations.push({ decoration, match, dispose() { decoration.dispose(); } });\n      }\n    }\n  }\n\n  private _find(term: string, startRow: number, startCol: number, searchOptions?: ISearchOptions): ISearchResult | undefined {\n    if (!this._terminal || !term || term.length === 0) {\n      this._terminal?.clearSelection();\n      this.clearDecorations();\n      return undefined;\n    }\n    if (startCol > this._terminal.cols) {\n      throw new Error(`Invalid col: ${startCol} to search in terminal of ${this._terminal.cols} cols`);\n    }\n\n    let result: ISearchResult | undefined = undefined;\n\n    this._initLinesCache();\n\n    const searchPosition: ISearchPosition = {\n      startRow,\n      startCol\n    };\n\n    // Search startRow\n    result = this._findInLine(term, searchPosition, searchOptions);\n    // Search from startRow + 1 to end\n    if (!result) {\n\n      for (let y = startRow + 1; y < this._terminal.buffer.active.baseY + this._terminal.rows; y++) {\n        searchPosition.startRow = y;\n        searchPosition.startCol = 0;\n        // If the current line is wrapped line, increase index of column to ignore the previous scan\n        // Otherwise, reset beginning column index to zero with set new unwrapped line index\n        result = this._findInLine(term, searchPosition, searchOptions);\n        if (result) {\n          break;\n        }\n      }\n    }\n    return result;\n  }\n\n  private _findNextAndSelect(term: string, searchOptions?: ISearchOptions): boolean {\n    if (!this._terminal || !term || term.length === 0) {\n      this._terminal?.clearSelection();\n      this.clearDecorations();\n      return false;\n    }\n\n    const prevSelectedPos = this._terminal.getSelectionPosition();\n    this._terminal.clearSelection();\n\n    let startCol = 0;\n    let startRow = 0;\n    if (prevSelectedPos) {\n      if (this._cachedSearchTerm === term) {\n        startCol = prevSelectedPos.end.x;\n        startRow = prevSelectedPos.end.y;\n      } else {\n        startCol = prevSelectedPos.start.x;\n        startRow = prevSelectedPos.start.y;\n      }\n    }\n\n    this._initLinesCache();\n\n    const searchPosition: ISearchPosition = {\n      startRow,\n      startCol\n    };\n\n    // Search startRow\n    let result = this._findInLine(term, searchPosition, searchOptions);\n    // Search from startRow + 1 to end\n    if (!result) {\n\n      for (let y = startRow + 1; y < this._terminal.buffer.active.baseY + this._terminal.rows; y++) {\n        searchPosition.startRow = y;\n        searchPosition.startCol = 0;\n        // If the current line is wrapped line, increase index of column to ignore the previous scan\n        // Otherwise, reset beginning column index to zero with set new unwrapped line index\n        result = this._findInLine(term, searchPosition, searchOptions);\n        if (result) {\n          break;\n        }\n      }\n    }\n    // If we hit the bottom and didn't search from the very top wrap back up\n    if (!result && startRow !== 0) {\n      for (let y = 0; y < startRow; y++) {\n        searchPosition.startRow = y;\n        searchPosition.startCol = 0;\n        result = this._findInLine(term, searchPosition, searchOptions);\n        if (result) {\n          break;\n        }\n      }\n    }\n\n    // If there is only one result, wrap back and return selection if it exists.\n    if (!result && prevSelectedPos) {\n      searchPosition.startRow = prevSelectedPos.start.y;\n      searchPosition.startCol = 0;\n      result = this._findInLine(term, searchPosition, searchOptions);\n    }\n\n    // Set selection and scroll if a result was found\n    return this._selectResult(result, searchOptions?.decorations, searchOptions?.noScroll);\n  }\n  /**\n   * Find the previous instance of the term, then scroll to and select it. If it\n   * doesn't exist, do nothing.\n   * @param term The search term.\n   * @param searchOptions Search options.\n   * @returns Whether a result was found.\n   */\n  public findPrevious(term: string, searchOptions?: ISearchOptions): boolean {\n    if (!this._terminal) {\n      throw new Error('Cannot use addon until it has been loaded');\n    }\n    const didOptionsChanged = this._lastSearchOptions ? this._didOptionsChange(this._lastSearchOptions, searchOptions) : true;\n    this._lastSearchOptions = searchOptions;\n    if (searchOptions?.decorations) {\n      if (this._cachedSearchTerm === undefined || term !== this._cachedSearchTerm || didOptionsChanged) {\n        this._highlightAllMatches(term, searchOptions);\n      }\n    }\n\n    const found = this._findPreviousAndSelect(term, searchOptions);\n    this._fireResults(searchOptions);\n    this._cachedSearchTerm = term;\n\n    return found;\n  }\n\n  private _didOptionsChange(lastSearchOptions: ISearchOptions, searchOptions?: ISearchOptions): boolean {\n    if (!searchOptions) {\n      return false;\n    }\n    if (lastSearchOptions.caseSensitive !== searchOptions.caseSensitive) {\n      return true;\n    }\n    if (lastSearchOptions.regex !== searchOptions.regex) {\n      return true;\n    }\n    if (lastSearchOptions.wholeWord !== searchOptions.wholeWord) {\n      return true;\n    }\n    return false;\n  }\n\n  private _fireResults(searchOptions?: ISearchOptions): void {\n    if (searchOptions?.decorations) {\n      let resultIndex = -1;\n      if (this._selectedDecoration.value) {\n        const selectedMatch = this._selectedDecoration.value.match;\n        for (let i = 0; i < this._highlightDecorations.length; i++) {\n          const match = this._highlightDecorations[i].match;\n          if (match.row === selectedMatch.row && match.col === selectedMatch.col && match.size === selectedMatch.size) {\n            resultIndex = i;\n            break;\n          }\n        }\n      }\n      this._onDidChangeResults.fire({ resultIndex, resultCount: this._highlightDecorations.length });\n    }\n  }\n\n  private _findPreviousAndSelect(term: string, searchOptions?: ISearchOptions): boolean {\n    if (!this._terminal) {\n      throw new Error('Cannot use addon until it has been loaded');\n    }\n    if (!this._terminal || !term || term.length === 0) {\n      this._terminal?.clearSelection();\n      this.clearDecorations();\n      return false;\n    }\n\n    const prevSelectedPos = this._terminal.getSelectionPosition();\n    this._terminal.clearSelection();\n\n    let startRow = this._terminal.buffer.active.baseY + this._terminal.rows - 1;\n    let startCol = this._terminal.cols;\n    const isReverseSearch = true;\n\n    this._initLinesCache();\n    const searchPosition: ISearchPosition = {\n      startRow,\n      startCol\n    };\n\n    let result: ISearchResult | undefined;\n    if (prevSelectedPos) {\n      searchPosition.startRow = startRow = prevSelectedPos.start.y;\n      searchPosition.startCol = startCol = prevSelectedPos.start.x;\n      if (this._cachedSearchTerm !== term) {\n        // Try to expand selection to right first.\n        result = this._findInLine(term, searchPosition, searchOptions, false);\n        if (!result) {\n          // If selection was not able to be expanded to the right, then try reverse search\n          searchPosition.startRow = startRow = prevSelectedPos.end.y;\n          searchPosition.startCol = startCol = prevSelectedPos.end.x;\n        }\n      }\n    }\n\n    if (!result) {\n      result = this._findInLine(term, searchPosition, searchOptions, isReverseSearch);\n    }\n\n    // Search from startRow - 1 to top\n    if (!result) {\n      searchPosition.startCol = Math.max(searchPosition.startCol, this._terminal.cols);\n      for (let y = startRow - 1; y >= 0; y--) {\n        searchPosition.startRow = y;\n        result = this._findInLine(term, searchPosition, searchOptions, isReverseSearch);\n        if (result) {\n          break;\n        }\n      }\n    }\n    // If we hit the top and didn't search from the very bottom wrap back down\n    if (!result && startRow !== (this._terminal.buffer.active.baseY + this._terminal.rows - 1)) {\n      for (let y = (this._terminal.buffer.active.baseY + this._terminal.rows - 1); y >= startRow; y--) {\n        searchPosition.startRow = y;\n        result = this._findInLine(term, searchPosition, searchOptions, isReverseSearch);\n        if (result) {\n          break;\n        }\n      }\n    }\n\n    // Set selection and scroll if a result was found\n    return this._selectResult(result, searchOptions?.decorations, searchOptions?.noScroll);\n  }\n\n  /**\n   * Sets up a line cache with a ttl\n   */\n  private _initLinesCache(): void {\n    const terminal = this._terminal!;\n    if (!this._linesCache) {\n      this._linesCache = new Array(terminal.buffer.active.length);\n      this._linesCacheDisposables.value = getDisposeArrayDisposable([\n        terminal.onLineFeed(() => this._destroyLinesCache()),\n        terminal.onCursorMove(() => this._destroyLinesCache()),\n        terminal.onResize(() => this._destroyLinesCache())\n      ]);\n    }\n\n    window.clearTimeout(this._linesCacheTimeoutId);\n    this._linesCacheTimeoutId = window.setTimeout(() => this._destroyLinesCache(), LINES_CACHE_TIME_TO_LIVE);\n  }\n\n  private _destroyLinesCache(): void {\n    this._linesCache = undefined;\n    this._linesCacheDisposables.clear();\n    if (this._linesCacheTimeoutId) {\n      window.clearTimeout(this._linesCacheTimeoutId);\n      this._linesCacheTimeoutId = 0;\n    }\n  }\n\n  /**\n   * A found substring is a whole word if it doesn't have an alphanumeric character directly\n   * adjacent to it.\n   * @param searchIndex starting indext of the potential whole word substring\n   * @param line entire string in which the potential whole word was found\n   * @param term the substring that starts at searchIndex\n   */\n  private _isWholeWord(searchIndex: number, line: string, term: string): boolean {\n    return ((searchIndex === 0) || (NON_WORD_CHARACTERS.includes(line[searchIndex - 1]))) &&\n      (((searchIndex + term.length) === line.length) || (NON_WORD_CHARACTERS.includes(line[searchIndex + term.length])));\n  }\n\n  /**\n   * Searches a line for a search term. Takes the provided terminal line and searches the text line,\n   * which may contain subsequent terminal lines if the text is wrapped. If the provided line number\n   * is part of a wrapped text line that started on an earlier line then it is skipped since it will\n   * be properly searched when the terminal line that the text starts on is searched.\n   * @param term The search term.\n   * @param searchPosition The position to start the search.\n   * @param searchOptions Search options.\n   * @param isReverseSearch Whether the search should start from the right side of the terminal and\n   * search to the left.\n   * @returns The search result if it was found.\n   */\n  protected _findInLine(term: string, searchPosition: ISearchPosition, searchOptions: ISearchOptions = {}, isReverseSearch: boolean = false): ISearchResult | undefined {\n    const terminal = this._terminal!;\n    const row = searchPosition.startRow;\n    const col = searchPosition.startCol;\n\n    // Ignore wrapped lines, only consider on unwrapped line (first row of command string).\n    const firstLine = terminal.buffer.active.getLine(row);\n    if (firstLine?.isWrapped) {\n      if (isReverseSearch) {\n        searchPosition.startCol += terminal.cols;\n        return;\n      }\n\n      // This will iterate until we find the line start.\n      // When we find it, we will search using the calculated start column.\n      searchPosition.startRow--;\n      searchPosition.startCol += terminal.cols;\n      return this._findInLine(term, searchPosition, searchOptions);\n    }\n    let cache = this._linesCache?.[row];\n    if (!cache) {\n      cache = this._translateBufferLineToStringWithWrap(row, true);\n      if (this._linesCache) {\n        this._linesCache[row] = cache;\n      }\n    }\n    const [stringLine, offsets] = cache;\n\n    const offset = this._bufferColsToStringOffset(row, col);\n    const searchTerm = searchOptions.caseSensitive ? term : term.toLowerCase();\n    const searchStringLine = searchOptions.caseSensitive ? stringLine : stringLine.toLowerCase();\n\n    let resultIndex = -1;\n    if (searchOptions.regex) {\n      const searchRegex = RegExp(searchTerm, 'g');\n      let foundTerm: RegExpExecArray | null;\n      if (isReverseSearch) {\n        // This loop will get the resultIndex of the _last_ regex match in the range 0..offset\n        while (foundTerm = searchRegex.exec(searchStringLine.slice(0, offset))) {\n          resultIndex = searchRegex.lastIndex - foundTerm[0].length;\n          term = foundTerm[0];\n          searchRegex.lastIndex -= (term.length - 1);\n        }\n      } else {\n        foundTerm = searchRegex.exec(searchStringLine.slice(offset));\n        if (foundTerm && foundTerm[0].length > 0) {\n          resultIndex = offset + (searchRegex.lastIndex - foundTerm[0].length);\n          term = foundTerm[0];\n        }\n      }\n    } else {\n      if (isReverseSearch) {\n        if (offset - searchTerm.length >= 0) {\n          resultIndex = searchStringLine.lastIndexOf(searchTerm, offset - searchTerm.length);\n        }\n      } else {\n        resultIndex = searchStringLine.indexOf(searchTerm, offset);\n      }\n    }\n\n    if (resultIndex >= 0) {\n      if (searchOptions.wholeWord && !this._isWholeWord(resultIndex, searchStringLine, term)) {\n        return;\n      }\n\n      // Adjust the row number and search index if needed since a \"line\" of text can span multiple\n      // rows\n      let startRowOffset = 0;\n      while (startRowOffset < offsets.length - 1 && resultIndex >= offsets[startRowOffset + 1]) {\n        startRowOffset++;\n      }\n      let endRowOffset = startRowOffset;\n      while (endRowOffset < offsets.length - 1 && resultIndex + term.length >= offsets[endRowOffset + 1]) {\n        endRowOffset++;\n      }\n      const startColOffset = resultIndex - offsets[startRowOffset];\n      const endColOffset = resultIndex + term.length - offsets[endRowOffset];\n      const startColIndex = this._stringLengthToBufferSize(row + startRowOffset, startColOffset);\n      const endColIndex = this._stringLengthToBufferSize(row + endRowOffset, endColOffset);\n      const size = endColIndex - startColIndex + terminal.cols * (endRowOffset - startRowOffset);\n\n      return {\n        term,\n        col: startColIndex,\n        row: row + startRowOffset,\n        size\n      };\n    }\n  }\n\n  private _stringLengthToBufferSize(row: number, offset: number): number {\n    const line = this._terminal!.buffer.active.getLine(row);\n    if (!line) {\n      return 0;\n    }\n    for (let i = 0; i < offset; i++) {\n      const cell = line.getCell(i);\n      if (!cell) {\n        break;\n      }\n      // Adjust the searchIndex to normalize emoji into single chars\n      const char = cell.getChars();\n      if (char.length > 1) {\n        offset -= char.length - 1;\n      }\n      // Adjust the searchIndex for empty characters following wide unicode\n      // chars (eg. CJK)\n      const nextCell = line.getCell(i + 1);\n      if (nextCell && nextCell.getWidth() === 0) {\n        offset++;\n      }\n    }\n    return offset;\n  }\n\n  private _bufferColsToStringOffset(startRow: number, cols: number): number {\n    const terminal = this._terminal!;\n    let lineIndex = startRow;\n    let offset = 0;\n    let line = terminal.buffer.active.getLine(lineIndex);\n    while (cols > 0 && line) {\n      for (let i = 0; i < cols && i < terminal.cols; i++) {\n        const cell = line.getCell(i);\n        if (!cell) {\n          break;\n        }\n        if (cell.getWidth()) {\n          // Treat null characters as whitespace to align with the translateToString API\n          offset += cell.getCode() === 0 ? 1 : cell.getChars().length;\n        }\n      }\n      lineIndex++;\n      line = terminal.buffer.active.getLine(lineIndex);\n      if (line && !line.isWrapped) {\n        break;\n      }\n      cols -= terminal.cols;\n    }\n    return offset;\n  }\n\n  /**\n   * Translates a buffer line to a string, including subsequent lines if they are wraps.\n   * Wide characters will count as two columns in the resulting string. This\n   * function is useful for getting the actual text underneath the raw selection\n   * position.\n   * @param lineIndex The index of the line being translated.\n   * @param trimRight Whether to trim whitespace to the right.\n   */\n  private _translateBufferLineToStringWithWrap(lineIndex: number, trimRight: boolean): LineCacheEntry {\n    const terminal = this._terminal!;\n    const strings = [];\n    const lineOffsets = [0];\n    let line = terminal.buffer.active.getLine(lineIndex);\n    while (line) {\n      const nextLine = terminal.buffer.active.getLine(lineIndex + 1);\n      const lineWrapsToNext = nextLine ? nextLine.isWrapped : false;\n      let string = line.translateToString(!lineWrapsToNext && trimRight);\n      if (lineWrapsToNext && nextLine) {\n        const lastCell = line.getCell(line.length - 1);\n        const lastCellIsNull = lastCell && lastCell.getCode() === 0 && lastCell.getWidth() === 1;\n        // a wide character wrapped to the next line\n        if (lastCellIsNull && nextLine.getCell(0)?.getWidth() === 2) {\n          string = string.slice(0, -1);\n        }\n      }\n      strings.push(string);\n      if (lineWrapsToNext) {\n        lineOffsets.push(lineOffsets[lineOffsets.length - 1] + string.length);\n      } else {\n        break;\n      }\n      lineIndex++;\n      line = nextLine;\n    }\n    return [strings.join(''), lineOffsets];\n  }\n\n  /**\n   * Selects and scrolls to a result.\n   * @param result The result to select.\n   * @returns Whether a result was selected.\n   */\n  private _selectResult(result: ISearchResult | undefined, options?: ISearchDecorationOptions, noScroll?: boolean): boolean {\n    const terminal = this._terminal!;\n    this._selectedDecoration.clear();\n    if (!result) {\n      terminal.clearSelection();\n      return false;\n    }\n    terminal.select(result.col, result.row, result.size);\n    if (options) {\n      const marker = terminal.registerMarker(-terminal.buffer.active.baseY - terminal.buffer.active.cursorY + result.row);\n      if (marker) {\n        const decoration = terminal.registerDecoration({\n          marker,\n          x: result.col,\n          width: result.size,\n          backgroundColor: options.activeMatchBackground,\n          layer: 'top',\n          overviewRulerOptions: {\n            color: options.activeMatchColorOverviewRuler\n          }\n        });\n        if (decoration) {\n          const disposables: IDisposable[] = [];\n          disposables.push(marker);\n          disposables.push(decoration.onRender((e) => this._applyStyles(e, options.activeMatchBorder, true)));\n          disposables.push(decoration.onDispose(() => disposeArray(disposables)));\n          this._selectedDecoration.value = { decoration, match: result, dispose() { decoration.dispose(); } };\n        }\n      }\n    }\n\n    if (!noScroll) {\n      // If it is not in the viewport then we scroll else it just gets selected\n      if (result.row >= (terminal.buffer.active.viewportY + terminal.rows) || result.row < terminal.buffer.active.viewportY) {\n        let scroll = result.row - terminal.buffer.active.viewportY;\n        scroll -= Math.floor(terminal.rows / 2);\n        terminal.scrollLines(scroll);\n      }\n    }\n    return true;\n  }\n\n  /**\n   * Applies styles to the decoration when it is rendered.\n   * @param element The decoration's element.\n   * @param borderColor The border color to apply.\n   * @param isActiveResult Whether the element is part of the active search result.\n   * @returns\n   */\n  private _applyStyles(element: HTMLElement, borderColor: string | undefined, isActiveResult: boolean): void {\n    if (!element.classList.contains('xterm-find-result-decoration')) {\n      element.classList.add('xterm-find-result-decoration');\n      if (borderColor) {\n        element.style.outline = `1px solid ${borderColor}`;\n      }\n    }\n    if (isActiveResult) {\n      element.classList.add('xterm-find-active-result-decoration');\n    }\n  }\n\n  /**\n   * Creates a decoration for the result and applies styles\n   * @param result the search result for which to create the decoration\n   * @param options the options for the decoration\n   * @returns the {@link IDecoration} or undefined if the marker has already been disposed of\n   */\n  private _createResultDecoration(result: ISearchResult, options: ISearchDecorationOptions): IDecoration | undefined {\n    const terminal = this._terminal!;\n    const marker = terminal.registerMarker(-terminal.buffer.active.baseY - terminal.buffer.active.cursorY + result.row);\n    if (!marker) {\n      return undefined;\n    }\n    const findResultDecoration = terminal.registerDecoration({\n      marker,\n      x: result.col,\n      width: result.size,\n      backgroundColor: options.matchBackground,\n      overviewRulerOptions: this._highlightedLines.has(marker.line) ? undefined : {\n        color: options.matchOverviewRuler,\n        position: 'center'\n      }\n    });\n    if (findResultDecoration) {\n      const disposables: IDisposable[] = [];\n      disposables.push(marker);\n      disposables.push(findResultDecoration.onRender((e) => this._applyStyles(e, options.matchBorder, false)));\n      disposables.push(findResultDecoration.onDispose(() => disposeArray(disposables)));\n    }\n    return findResultDecoration;\n  }\n}\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "_listeners", "_disposed", "event", "this", "_event", "listener", "push", "dispose", "i", "length", "splice", "fire", "arg1", "arg2", "queue", "call", "undefined", "clearListeners", "from", "to", "e", "handler", "dispose<PERSON><PERSON><PERSON>", "disposables", "d", "_disposables", "_isDisposed", "register", "unregister", "index", "indexOf", "value", "_value", "clear", "f", "array", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "NON_WORD_CHARACTERS", "SearchAddon", "Disposable", "constructor", "options", "super", "_highlightedLines", "Set", "_highlightDecorations", "_selectedDecoration", "MutableDisposable", "_linesCacheTimeoutId", "_linesCacheDisposables", "_onDidChangeResults", "EventEmitter", "onDidChangeResults", "_highlightLimit", "highlightLimit", "activate", "terminal", "_terminal", "onWriteParsed", "_updateMatches", "onResize", "toDisposable", "clearDecorations", "_highlightTimeout", "window", "clearTimeout", "_cachedSearchTerm", "_lastSearchOptions", "decorations", "setTimeout", "term", "find<PERSON>revious", "incremental", "noScroll", "retainCachedSearchTerm", "clearActiveDecoration", "findNext", "searchOptions", "Error", "didOptionsChanged", "_didOptionsChange", "_highlightAllMatches", "found", "_findNextAndSelect", "_fireResults", "searchResultsWithHighlight", "prevResult", "result", "_find", "row", "col", "cols", "match", "decoration", "_createResultDecoration", "add", "marker", "line", "startRow", "startCol", "clearSelection", "_initLinesCache", "searchPosition", "_findInLine", "y", "buffer", "active", "baseY", "rows", "prevSelectedPos", "getSelectionPosition", "end", "x", "start", "_selectResult", "_findPreviousAndSelect", "lastSearchOptions", "caseSensitive", "regex", "wholeWord", "resultIndex", "selectedM<PERSON>", "size", "resultCount", "isReverseSearch", "Math", "max", "_linesCache", "Array", "getDisposeArrayDisposable", "onLineFeed", "_destroyLinesCache", "onCursorMove", "_isWholeWord", "searchIndex", "includes", "firstLine", "getLine", "isWrapped", "cache", "_translateBufferLineToStringWithWrap", "stringLine", "offsets", "offset", "_bufferColsToStringOffset", "searchTerm", "toLowerCase", "searchStringLine", "searchRegex", "RegExp", "foundTerm", "exec", "slice", "lastIndex", "lastIndexOf", "startRowOffset", "endRowOffset", "startColOffset", "endColOffset", "startColIndex", "_stringLengthToBufferSize", "cell", "getCell", "char", "getChars", "nextCell", "getWidth", "lineIndex", "getCode", "trimRight", "strings", "lineOffsets", "nextLine", "lineWrapsToNext", "string", "translateToString", "lastCell", "join", "select", "registerMarker", "cursorY", "registerDecoration", "width", "backgroundColor", "activeMatchBackground", "layer", "overviewRulerOptions", "color", "activeMatchColorOverviewRuler", "onRender", "_applyStyles", "activeMatchBorder", "onDispose", "viewportY", "scroll", "floor", "scrollLines", "element", "borderColor", "isActiveResult", "classList", "contains", "style", "outline", "findResultDecoration", "matchBackground", "has", "matchOverviewRuler", "position", "matchBorder"], "sourceRoot": ""}